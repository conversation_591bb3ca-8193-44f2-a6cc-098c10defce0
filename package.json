{"name": "portfolio", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "postinstall": "cd node_modules/fabric && npm run build_with_gestures", "watch": "ng build --watch --configuration development", "deploy": "ng build  --base-href='' && ngh --dir=dist/portfolio/browser", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^18.2.0", "@angular/cdk": "^18.2.13", "@angular/common": "^18.2.0", "@angular/compiler": "^18.2.0", "@angular/core": "^18.2.0", "@angular/flex-layout": "^15.0.0-beta.42", "@angular/forms": "^18.2.0", "@angular/material": "^18.2.13", "@angular/platform-browser": "^18.2.0", "@angular/platform-browser-dynamic": "^18.2.0", "@angular/router": "^18.2.0", "@ngneat/overview": "^6.0.0", "@ngrx/effects": "^18.1.1", "@ngrx/store": "^18.1.1", "@ngxpert/hot-toast": "^3.0.2", "@types/raphael": "^2.3.9", "bezier-js": "^6.1.4", "canvas-confetti": "^1.9.3", "fabric": "5.4.2-browser", "laser-pen": "^1.0.1", "lodash": "^4.17.21", "ng-circle-progress": "^1.7.1", "ng-otp-input": "^1.9.3", "perfect-freehand": "^1.2.2", "raphael": "^2.3.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.14.10"}, "devDependencies": {"@angular-devkit/build-angular": "^18.2.10", "@angular/cli": "^18.2.10", "@angular/compiler-cli": "^18.2.0", "@types/bezier-js": "^4.1.3", "@types/canvas-confetti": "^1.6.4", "@types/fabric": "^5.3.8", "@types/jasmine": "~5.1.0", "@types/lodash": "^4.14.191", "jasmine-core": "~5.2.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.5.2"}}