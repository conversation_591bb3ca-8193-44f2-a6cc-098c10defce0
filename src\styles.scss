/* You can add global styles to this file, and also import other style files */
@use '@ngxpert/hot-toast/src/styles/styles.scss';
@import './styles/reset';
app-root{
  width: 100%;
  height: 100%;
}
.ng-otp-input-wrapper{
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
 }
.otp-input{
  width: 39px !important;
  height: 39px !important;
  font-size: 26px !important;
}


html, body { height: 100%; }
body { margin: 0; font-family: Roboto, "Helvetica Neue", sans-serif; }

/* Firefox (uncomment to work in Firefox, although other properties will not work!)  */
/** {
  scrollbar-width: thin;
  scrollbar-color: #1A237E #DFE9EB;
}*/
/* Chrome, Edge, Safari */
*::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

*::-webkit-scrollbar-track {
  border-radius: 10px;
  background: #f3f4f6; /* Light gray for the track */
}

*::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: #9fa8da; /* Light blue for the thumb */
  box-shadow: inset 0 0 4px rgba(0, 0, 0, 0.1); /* Subtle shadow */
}

*::-webkit-scrollbar-thumb:hover {
  background: #7986cb; /* Slightly darker blue on hover */
}

*::-webkit-scrollbar-thumb:active {
  background: #5c6bc0; /* Even darker blue when active */
}

.canvas-container{
  canvas{
    border-top-left-radius: 20px;
    border-bottom-left-radius: 20px;

  }
}

.cdk-overlay-dark-backdrop{
  background: #312C4F;
}
