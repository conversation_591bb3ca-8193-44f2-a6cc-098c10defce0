\documentclass[10pt, letterpaper]{article}

% Packages:
\usepackage[
    ignoreheadfoot, % set margins without considering header and footer
    top=2 cm, % seperation between body and page edge from the top
    bottom=2 cm, % seperation between body and page edge from the bottom
    left=2 cm, % seperation between body and page edge from the left
    right=2 cm, % seperation between body and page edge from the right
    footskip=1.0 cm, % seperation between body and footer
    % showframe % for debugging
]{geometry} % for adjusting page geometry
\usepackage{titlesec} % for customizing section titles
\usepackage{tabularx} % for making tables with fixed width columns
\usepackage{array} % tabularx requires this
\usepackage[dvipsnames]{xcolor} % for coloring text
\definecolor{primaryColor}{RGB}{0, 0, 0} % define primary color
\usepackage{enumitem} % for customizing lists
\usepackage{fontawesome5} % for using icons
\usepackage{amsmath} % for math
\usepackage[
    pdftitle={<PERSON>'s CV},
    pdfauthor={<PERSON>},
    pdfcreator={LaTeX with RenderCV},
    colorlinks=true,
    urlcolor=primaryColor
]{hyperref} % for links, metadata and bookmarks
\usepackage[pscoord]{eso-pic} % for floating text on the page
\usepackage{calc} % for calculating lengths
\usepackage{bookmark} % for bookmarks
\usepackage{lastpage} % for getting the total number of pages
\usepackage{changepage} % for one column entries (adjustwidth environment)
\usepackage{paracol} % for two and three column entries
\usepackage{ifthen} % for conditional statements
\usepackage{needspace} % for avoiding page brake right after the section title
\usepackage{iftex} % check if engine is pdflatex, xetex or luatex

% Ensure that generate pdf is machine readable/ATS parsable:
\ifPDFTeX
    \input{glyphtounicode}
    \pdfgentounicode=1
    \usepackage[T1]{fontenc}
    \usepackage[utf8]{inputenc}
    \usepackage{lmodern}
\fi

\usepackage{charter}

% Some settings:
\raggedright
\AtBeginEnvironment{adjustwidth}{\partopsep0pt} % remove space before adjustwidth environment
\pagestyle{empty} % no header or footer
\setcounter{secnumdepth}{0} % no section numbering
\setlength{\parindent}{0pt} % no indentation
\setlength{\topskip}{0pt} % no top skip
\setlength{\columnsep}{0.15cm} % set column seperation
\pagenumbering{gobble} % no page numbering

\titleformat{\section}{\needspace{4\baselineskip}\bfseries\large}{}{0pt}{}[\vspace{1pt}\titlerule]

\titlespacing{\section}{
    % left space:
    -1pt
}{
    % top space:
    0.3 cm
}{
    % bottom space:
    0.2 cm
} % section title spacing

\renewcommand\labelitemi{$\vcenter{\hbox{\small$\bullet$}}$} % custom bullet points
\newenvironment{highlights}{
    \begin{itemize}[
        topsep=0.10 cm,
        parsep=0.10 cm,
        partopsep=0pt,
        itemsep=0pt,
        leftmargin=0 cm + 10pt
    ]
}{
    \end{itemize}
} % new environment for highlights


\newenvironment{highlightsforbulletentries}{
    \begin{itemize}[
        topsep=0.10 cm,
        parsep=0.10 cm,
        partopsep=0pt,
        itemsep=0pt,
        leftmargin=10pt
    ]
}{
    \end{itemize}
} % new environment for highlights for bullet entries

\newenvironment{onecolentry}{
    \begin{adjustwidth}{
        0 cm + 0.00001 cm
    }{
        0 cm + 0.00001 cm
    }
}{
    \end{adjustwidth}
} % new environment for one column entries

\newenvironment{twocolentry}[2][]{
    \onecolentry
    \def\secondColumn{#2}
    \setcolumnwidth{\fill, 4.5 cm}
    \begin{paracol}{2}
}{
    \switchcolumn \raggedleft \secondColumn
    \end{paracol}
    \endonecolentry
} % new environment for two column entries

\newenvironment{threecolentry}[3][]{
    \onecolentry
    \def\thirdColumn{#3}
    \setcolumnwidth{, \fill, 4.5 cm}
    \begin{paracol}{3}
    {\raggedright #2} \switchcolumn
}{
    \switchcolumn \raggedleft \thirdColumn
    \end{paracol}
    \endonecolentry
} % new environment for three column entries

\newenvironment{header}{
    \setlength{\topsep}{0pt}\par\kern\topsep\centering\linespread{1.5}
}{
    \par\kern\topsep
} % new environment for the header

\newcommand{\placelastupdatedtext}{% \placetextbox{<horizontal pos>}{<vertical pos>}{<stuff>}
  \AddToShipoutPictureFG*{% Add <stuff> to current page foreground
    \put(
        \LenToUnit{\paperwidth-2 cm-0 cm+0.05cm},
        \LenToUnit{\paperheight-1.0 cm}
    ){\vtop{{\null}\makebox[0pt][c]{
        \small\color{gray}\textit{Last updated in September 2024}\hspace{\widthof{Last updated in September 2024}}
    }}}%
  }%
}%

% save the original href command in a new command:
\let\hrefWithoutArrow\href

% new command for external links:


\begin{document}
\newcommand{\AND}{\unskip
	\cleaders\copy\ANDbox\hskip\wd\ANDbox
	\ignorespaces
}
\newsavebox\ANDbox
\sbox\ANDbox{$|$}

\begin{header}
	\fontsize{25 pt}{25 pt}\selectfont Medapati Vijay Reddy

	\vspace{5 pt}

	\normalsize
	\mbox{Hyderabad}%
	\kern 5.0 pt%
	\AND%
	\kern 5.0 pt%
	\mbox{\hrefWithoutArrow{mailto:<EMAIL>}{<EMAIL>}}%
	\kern 5.0 pt%
	\AND%
	\kern 5.0 pt%
	\mbox{\hrefWithoutArrow{tel:+91-630-183-21-61}{6301832161}}%
	\kern 5.0 pt%
	\AND%
	\kern 5.0 pt%
	\mbox{\hrefWithoutArrow{vijay18399.github.io}{vijay18399.github.io}}%
	\kern 5.0 pt%
	\AND%
	\kern 5.0 pt%
	\mbox{\hrefWithoutArrow{https://linkedin.com/in/vijay18399}{linkedin.com/in/vijay18399}}%

\end{header}

\vspace{5 pt - 0.3 cm}


\section{Education}




\begin{twocolentry}{
		Jun 2016 – Sept 2020
	}
	\textbf{Aditya Engineering College}, B.Tech in Computer Science and Engineering\end{twocolentry}

	\vspace{0.10 cm}
	\begin{onecolentry}
		\begin{highlights}
			\item GPA: 8.01/10.0
		\end{highlights}
	\end{onecolentry}
	\section{Experience}
	\begin{twocolentry}{
			Aug 2022 – Present
		}
		\textbf{ Engineer (R \& D Department) }, Next Education -- Hyderabad, Telangana\end{twocolentry}

		\vspace{0.10 cm}
		\begin{onecolentry}
			\begin{highlights}
				\item Developed UI and client-side code for AI-powered English learning tools, integrating real-time APIs for speaking and writing modules.
				\item Reduced Time to copy content by 60\% by implementing a desktop App in Angular, Nodejs , Electron
				\item Integrated and extended the PanZoom library in the eBook project to support pinch zoom and  swipe gestures.
				\item Developed a tuxpaint web version on top of pre-existing legacy whiteboard (AngularJS , Fabric.js)
				\item Integrated and extended the Scratch (React) project to support multiple environments, enabling teachers to create questions and students to attempt them.
				\item Refactored and rebuilt a PDF/image annotation tool for cross-device syncing with pinch-to-zoom support.
			\end{highlights}
		\end{onecolentry}
		\vspace{0.2 cm}

		\begin{twocolentry}{
				Feb 2021 – Aug 2022
			}
			\textbf{Assistant System Engineer}, TCS -- Remote \end{twocolentry}

			\vspace{0.10 cm}
			\begin{onecolentry}
				\begin{highlights}
					\item Provided support for .NET-based projects, writing queries for generating reports, deleting faulty data based on client requirements.
					\item Contributed as part of a team for 6 months on researching and upgrading .NET projects for a client. Responsibilities included identifying server requirements, coordinating with stakeholders, and collaborating with the system team to strategize the upgrade process.

				\end{highlights}
			\end{onecolentry}

			\vspace{0.2 cm}

			\begin{twocolentry}{
				July 2019 - Sept 2019        }
				\textbf{Frontend Developer Intern
					}, Mounty -- Hyderabad \end{twocolentry}

				\vspace{0.10 cm}
				\begin{onecolentry}
					\begin{highlights}
						\item Developed a user-friendly interface for campsite owners to upload details and images
						\item Refactored the codebase to introduce reusable components, enhancing code maintainability and
						operational efficiency across the project.


					\end{highlights}
				\end{onecolentry}
				\section{Publications}
				\begin{samepage}
					\begin{twocolentry}{
							Sept 2020
						}
						\textbf{An Enhanced Messenger with Language Translator and Sentiment Analysis}
					\end{twocolentry}
					\vspace{0.10 cm}
					\begin{onecolentry}
						\mbox{\textbf{\textit{M Vijay Reddy}}},
						\mbox{N. Soundarya},
						\mbox{M.N.V Ravindra}
						\mbox{K. Sai Srujana}
						\vspace{0.10 cm}
						\href{https://www.ijasrw.com/pdf/ICROIRT20/IJConfA15.pdf}{International Journal of Advance Study and Research Work, Pages 98-107, ISSN: 2581-5997.}
					\end{onecolentry}
				\end{samepage}
				\section{Projects}
				\begin{twocolentry}{
						\href{https://vijay18399.github.io/ngBoard/}{View Project}
					}
					\textbf{NgBoard}
				\end{twocolentry}

				\vspace{0.10 cm}
				\begin{onecolentry}
					\begin{highlights}
						\item Built a digital whiteboard with tools like drawing, text annotations, laser, and custom brush for smooth strokes and paint bucket tool and pan zoom using events.
						\item Tools Used: Angular, Fabric.js
					\end{highlights}
				\end{onecolentry}
				\vspace{0.2 cm}
				\begin{twocolentry}{
						\href{https://vijay18399.github.io}{View Project}
					}
					\textbf{Spell Bee}
				\end{twocolentry}
				\vspace{0.10 cm}
				\begin{onecolentry}
					\begin{highlights}
						\item Built a mobile-first Spell Bee app enabling users to select word difficulty levels (A1 to C1), with an SQLite database and Node.js for dynamic word generation.
						\item Tools Used: Angular, Angular Material, ngrx, Node.js, SQLite.
					\end{highlights}
				\end{onecolentry}
				\section{Technologies}
				\begin{onecolentry}
					\textbf{Frameworks \& Libraries:}  Angular, Angular Material, Node.js, Electron.js, Fabric.js, jQuery, SQLite
				\end{onecolentry}
\end{document}
