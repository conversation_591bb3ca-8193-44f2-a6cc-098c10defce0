<?xml version="1.0" ?>

<!-- Uploaded to: SVG Repo, www.svgrepo.com, Generator: SVG Repo Mixer Tools -->
<svg width="800px" height="800px" viewBox="0 0 128 128" version="1.1" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">

<style type="text/css">
	.st0{fill:#69A401;}
	.st1{fill:#EFE691;}
	.st2{fill:#B20000;}
	.st3{fill:#DF1801;}
	.st4{fill:#F40603;}
	.st5{fill:#FFEEEE;}
	.st6{fill:#847B3C;}
	.st7{fill:#CEB600;}
	.st8{fill:#F8CD02;}
	.st9{fill:#F7C800;}
	.st10{fill:#F6E8B9;}
	.st11{fill:#F6E9CA;}
	.st12{fill:#CF8A11;}
	.st13{fill:#286F0D;}
	.st14{fill:#63271D;}
	.st15{fill:#EB8102;}
	.st16{fill:#E37303;}
	.st17{fill:#D97102;}
	.st18{fill:#BF6302;}
	.st19{fill:#EA9735;}
	.st20{fill:#3E1A01;}
	.st21{fill:#C96A0A;}
	.st22{fill:#CE2335;}
	.st23{fill:#C0242D;}
	.st24{fill:#BA1A23;}
	.st25{fill:#F9DCC7;}
	.st26{fill:#DBE2CE;}
	.st27{fill:#7D4B12;}
	.st28{fill:#75480C;}
	.st29{fill:#66410C;}
	.st30{fill:#88550D;}
	.st31{fill:#FFFEE9;}
	.st32{fill:#9B9F1A;}
	.st33{fill:#F6E177;}
	.st34{fill:#443A00;}
	.st35{fill:#305209;}
	.st36{fill:#7F7C04;}
	.st37{fill:#BAB424;}
	.st38{fill:#F7CF43;}
	.st39{fill:#DE940E;}
	.st40{fill:#5F570A;}
	.st41{fill:#175424;}
	.st42{fill:#215B25;}
	.st43{fill:#1B5020;}
	.st44{fill:#C0F9C0;}
	.st45{fill:#F3DA78;}
	.st46{fill:#BC441C;}
	.st47{fill:#148E2E;}
	.st48{fill:#283767;}
	.st49{fill:#425285;}
	.st50{fill:#CFDFFF;}
	.st51{fill:#1F2C55;}
	.st52{fill:#776220;}
	.st53{fill:#90236B;}
	.st54{fill:#5D1A47;}
	.st55{fill:#99499A;}
	.st56{fill:#FCCAFA;}
	.st57{fill:#917C31;}
	.st58{fill:#F4C435;}
	.st59{fill:#F1BC02;}
	.st60{fill:#F0B102;}
	.st61{fill:#F1F7BA;}
	.st62{fill:#E3DCB9;}
	.st63{fill:#BD6800;}
	.st64{fill:#E19704;}
	.st65{fill:#B2CA2B;}
	.st66{fill:#AFC20F;}
	.st67{fill:#B9CB00;}
	.st68{fill:#E5F392;}
	.st69{fill:#F78202;}
	.st70{fill:#F79613;}
	.st71{fill:#331F07;}
	.st72{fill:#402B16;}
	.st73{fill:#669404;}
	.st74{fill:#F58E13;}
	.st75{fill:#D87117;}
	.st76{fill:#216604;}
	.st77{fill:#286D08;}
	.st78{fill:#C8C625;}
	.st79{fill:#2C441F;}
	.st80{fill:#F1E6BF;}
	.st81{fill:#F2BE2E;}
	.st82{fill:#BF8F33;}
	.st83{fill:#568804;}
	.st84{fill:#669614;}
	.st85{fill:#688E0C;}
	.st86{fill:#4C7005;}
	.st87{fill:#A0CA49;}
	.st88{fill:#99BD70;}
	.st89{fill:#78AA25;}
	.st90{fill:#4B7C23;}
	.st91{fill:#EADBC8;}
	.st92{fill:#F0D5B0;}
	.st93{fill:#DF2B2B;}
	.st94{fill:#D1262C;}
	.st95{fill:#B7252C;}
	.st96{fill:#46670C;}
	.st97{fill:#F49D5B;}
	.st98{fill:#F57A55;}
	.st99{fill:#F1C3A7;}
	.st100{fill:#CC0917;}
	.st101{fill:#DC1035;}
	.st102{fill:#9BAC0F;}
	.st103{fill:#667A1D;}
	.st104{fill:#7A9D18;}
	.st105{fill:#F6F7E6;}
	.st106{fill:#F0194D;}
	.st107{fill:#362420;}
	.st108{fill:#530618;}
	.st109{fill:#44041A;}
	.st110{fill:#490419;}
	.st111{fill:#F8A459;}
	.st112{fill:#871B22;}
	.st113{fill:#600613;}
	.st114{fill:#F8C790;}
	.st115{fill:#447832;}
	.st116{fill:#7C473D;}
	.st117{fill:#441432;}
	.st118{fill:#51163F;}
	.st119{fill:#5B1A41;}
	.st120{fill:#FCEBF9;}
	.st121{fill:#ECE5CE;}
	.st122{fill:#BC3E2C;}
	.st123{fill:#A60F26;}
	.st124{fill:#C61632;}
	.st125{fill:#BD1331;}
	.st126{fill:#F8B772;}
	.st127{fill:#F7DDAC;}
	.st128{fill:#850E11;}
	.st129{fill:#191200;}
	.st130{fill:#553D2D;}
	.st131{fill:#F9E2D2;}
	.st132{fill:#CA8937;}
	.st133{fill:#462D16;}
	.st134{fill:#6D8916;}
	.st135{fill:#96B54E;}
	.st136{fill:#E3E2DE;}
	.st137{fill:#261811;}
	.st138{fill:#525C11;}
	.st139{fill:#14581E;}
	.st140{fill:#3D7712;}
	.st141{fill:#9BC148;}
	.st142{fill:#E22434;}
	.st143{fill:#C6DD9E;}
	.st144{fill:#F89A07;}
	.st145{fill:#F7A410;}
	.st146{fill:#F8AB19;}
	.st147{fill:#F7B81C;}
	.st148{fill:#E5870A;}
	.st149{fill:#97A304;}
	.st150{fill:#A88C5C;}
	.st151{fill:#ADC21E;}
	.st152{fill:#A3BA0B;}
	.st153{fill:#8D9E08;}
	.st154{fill:#E0DAB9;}
	.st155{fill:#684219;}
	.st156{fill:#777F05;}
	.st157{fill:#F2E9C4;}
	.st158{fill:#CBB465;}
	.st159{fill:#FFF5CA;}
	.st160{fill:#E52828;}
	.st161{fill:#F87302;}
	.st162{fill:#FF7B22;}
	.st163{fill:#FC7F10;}
	.st164{fill:#F8A200;}
	.st165{fill:#F8DC91;}
	.st166{fill:#FFFFFF;}
	.st167{fill:#F5D7D5;}
	.st168{fill:#EDA07A;}
	.st169{fill:#FCBEBE;}
	.st170{fill:#EAD991;}
	.st171{fill:#582612;}
</style>

<g id="_x33_0_Mulberry"/>

<g id="_x32_9_Star_Fruit"/>

<g id="_x32_8_Apricot"/>

<g id="_x32_7_Litchi"/>

<g id="_x32_6_Kiwi"/>

<g id="_x32_5_Jackfruit"/>

<g id="_x32_4_Avacado"/>

<g id="_x32_3_Blueberry"/>

<g id="_x32_2_Purple_Grapes"/>

<g id="_x32_1_Melon"/>

<g id="_x32_0_Green_Grapes"/>

<g id="_x31_9_Papaya"/>

<g id="_x31_8_Pineapple">

<g id="XMLID_971_">

<ellipse class="st74" cx="63.408" cy="77.292" id="XMLID_984_" rx="44.441" ry="48.708"/>

<g id="XMLID_1385_">

<path class="st75" d="M28.124,47.692c-0.798,1.141-1.548,2.323-2.253,3.54c0.002,0.001,0.003,0.003,0.005,0.004     c7.566,4.566,46.303,29.051,65.966,62.815c0.084,0.145,0.189,0.269,0.299,0.386c1.036-0.964,2.033-1.977,2.985-3.04     C74.934,77.158,36.012,52.461,28.124,47.692z" id="XMLID_1438_"/>

<path class="st75" d="M21.29,64.004c-0.183-0.111-0.378-0.18-0.576-0.23c-0.365,1.386-0.678,2.794-0.93,4.228     c9.852,6.034,40.074,26.068,58.879,55.052c1.3-0.521,2.569-1.11,3.809-1.757c-0.046-0.107-0.096-0.214-0.162-0.315     C62.608,90.471,30.624,69.658,21.29,64.004z" id="XMLID_1439_"/>

<path class="st75" d="M21.25,86.744c-0.461-0.285-0.989-0.36-1.483-0.266c0.296,1.697,0.667,3.363,1.119,4.993     c8.329,5.309,25.476,17.283,40.672,34.478c0.614,0.028,1.229,0.051,1.85,0.051c1.025,0,2.038-0.052,3.045-0.127     c-0.024-0.451-0.183-0.898-0.5-1.266C48.88,104.821,29.003,91.538,21.25,86.744z" id="XMLID_1462_"/>

<path class="st75" d="M40.938,35.274c-1.23,0.792-2.421,1.646-3.572,2.56c0.156,0.207,0.35,0.391,0.585,0.533     c7.205,4.359,44.138,27.793,63.697,61.153c0.222,0.378,0.548,0.648,0.916,0.821c0.684-1.393,1.308-2.828,1.868-4.298     C84.854,63.656,49.821,40.74,40.938,35.274z" id="XMLID_1466_"/>

<path class="st75" d="M107.792,74.826C95.33,54.528,77.812,38.794,64.298,28.609     c-0.297-0.006-0.591-0.025-0.889-0.025c-1.559,0-3.099,0.09-4.617,0.262c0.051,0.583,0.334,1.143,0.841,1.516     c14.096,10.343,33.59,27.357,46.408,49.67c0.36,0.627,0.995,0.982,1.663,1.031c0.087-1.246,0.145-2.5,0.145-3.77     C107.849,76.465,107.829,75.643,107.792,74.826z" id="XMLID_1467_"/>

</g>

<g id="XMLID_22_">

<path class="st75" d="M36.164,114.051c19.65-33.74,57.647-57.789,65.094-62.284     c-0.69-1.226-1.423-2.418-2.206-3.569c-10.726,6.479-46.866,30.029-66.492,63.729c-0.052,0.09-0.089,0.184-0.126,0.277     c0.881,0.939,1.791,1.846,2.74,2.703C35.573,114.736,35.928,114.456,36.164,114.051z" id="XMLID_1429_"/>

<path class="st75" d="M103.191,72.729l0.488-0.295c0.988-0.599,1.308-1.89,0.713-2.885     c-0.596-0.994-1.88-1.317-2.867-0.717l-0.486,0.294c-9.809,5.934-35.866,21.693-55.345,51.856     c-0.168,0.26-0.267,0.543-0.31,0.831c1.208,0.588,2.443,1.117,3.705,1.588c0.034-0.045,0.077-0.08,0.108-0.129     C68.151,93.922,93.609,78.525,103.191,72.729z" id="XMLID_1430_"/>

<path class="st75" d="M105.37,87.612c-9.116,5.634-26.868,17.932-43.317,36.995     c-0.335,0.388-0.494,0.865-0.502,1.341c0.617,0.028,1.234,0.051,1.857,0.051c1.052,0,2.092-0.054,3.126-0.133     c14.629-16.561,30.075-27.706,39.129-33.483c0.488-1.642,0.903-3.319,1.232-5.033C106.39,87.243,105.845,87.318,105.37,87.612z" id="XMLID_1431_"/>

<path class="st75" d="M20.945,82.525C33.67,60.373,54.448,40.581,68.374,30.362     c0.46-0.338,0.74-0.829,0.827-1.352c-1.787-0.255-3.605-0.401-5.452-0.416C49.663,39.341,31.41,57.197,18.976,77.672     c0.014,1.99,0.143,3.949,0.369,5.875C19.989,83.481,20.596,83.132,20.945,82.525z" id="XMLID_1433_"/>

<path class="st75" d="M24.991,100.293c18.706-31.907,53.697-55.079,63.48-60.967l0.935-0.564     c0.229-0.138,0.418-0.317,0.572-0.517c-0.013-0.011-0.028-0.021-0.041-0.032c-1.085-0.886-2.208-1.718-3.366-2.494     c-0.038-0.026-0.074-0.054-0.113-0.079l-0.132,0.08c-9.822,5.912-44.355,28.744-63.83,60.62c0.599,1.544,1.273,3.044,2.009,4.501     C24.691,100.688,24.862,100.513,24.991,100.293z" id="XMLID_542_"/>

</g>

<g id="XMLID_966_">

<path class="st76" d="M63.957,33.075c0,0-25.576-14.802-46.957,0c0,0,2.744-6.501,8.295-12.382     c12.577-13.327,35.414-6.908,38.481,11.144C63.845,32.242,63.905,32.655,63.957,33.075z" id="XMLID_970_"/>

<path class="st77" d="M63.957,33.075c0,0-14.65-25.664-40.58-23.516c0,0,5.632-4.26,13.385-6.583     c17.567-5.264,34.129,11.694,27.744,28.858C64.362,32.219,64.122,32.685,63.957,33.075z" id="XMLID_969_"/>

<path class="st76" d="M63.957,33.075c0,0,25.663-14.873,47.043-0.071c0,0-2.744-6.501-8.295-12.382     C90.128,7.295,67.292,13.714,64.224,31.766C64.155,32.171,64.008,32.655,63.957,33.075z" id="XMLID_968_"/>

<path class="st77" d="M63.952,33.075c0,0,14.802-25.617,40.732-23.47c0,0-5.632-4.26-13.385-6.583     C73.732-2.242,57.17,14.715,63.555,31.88C63.699,32.265,63.786,32.685,63.952,33.075z" id="XMLID_967_"/>

</g>

</g>

</g>

<g id="_x31_7_Banana"/>

<g id="_x31_6_Tender_Coconut"/>

<g id="_x31_5_Strawberry"/>

<g id="_x31_4_Dragon_Fruit"/>

<g id="_x31_3_Plum"/>

<g id="_x31_2_Fig"/>

<g id="_x31_1_Peach"/>

<g id="_x31_0_Cherry"/>

<g id="_x30_9_Sapota"/>

<g id="_x30_8_Custard_Apple"/>

<g id="_x30_7_Watermelon"/>

<g id="_x30_6_Mango"/>

<g id="_x30_5_Pear"/>

<g id="_x30_4_Guava"/>

<g id="_x30_3_Pomegranate"/>

<g id="_x30_2_Orange"/>

<g id="_x30_1_Apple"/>

</svg>